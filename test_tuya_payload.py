#!/usr/bin/env python3
"""
测试TuyaLink协议payload格式
"""

import json
import time

def test_tuya_payload_format():
    """测试TuyaLink协议的payload格式"""
    
    print("=== 测试TuyaLink协议payload格式 ===\n")
    
    # 模拟传感器读数
    lux_float = 123.45
    
    # 1. 错误的格式（修复前）
    wrong_payload = {
        "properties": {
            "lux": lux_float  # 小数值，且缺少外层结构
        }
    }
    
    print("1. 错误的格式（修复前）:")
    print(json.dumps(wrong_payload, indent=2))
    print("问题：")
    print("  - 缺少外层的 data、msgId、time 字段")
    print("  - lux 是小数值，但涂鸦'数值型'功能点只接受整数")
    print()
    
    # 2. 正确的格式（修复后）
    # 转换为整数，符合"数值型"功能点要求
    lux_int = int(round(max(0, min(lux_float, 65535))))  # 限制在0-65535范围内
    
    msg_seq = 12345
    now_ms = int(time.time() * 1000)  # 毫秒时间戳
    
    correct_payload = {
        "msgId": str(msg_seq),
        "time": now_ms,
        "data": {
            "properties": {
                "lux": lux_int  # 整数值
            }
        }
    }
    
    print("2. 正确的格式（修复后）:")
    print(json.dumps(correct_payload, indent=2))
    print("修复：")
    print("  - 添加了 msgId 和 time 字段")
    print("  - 添加了外层的 data 结构")
    print(f"  - lux 从小数 {lux_float} 转换为整数 {lux_int}")
    print()
    
    # 3. 验证JSON大小
    wrong_json = json.dumps(wrong_payload)
    correct_json = json.dumps(correct_payload)
    
    print("3. JSON大小对比:")
    print(f"  错误格式: {len(wrong_json)} 字节")
    print(f"  正确格式: {len(correct_json)} 字节")
    print(f"  增加: {len(correct_json) - len(wrong_json)} 字节")
    print()
    
    # 4. 模拟不同的传感器值
    print("4. 不同传感器值的转换测试:")
    test_values = [0.1, 1.9, 123.45, 999.99, 65535.1, -5.0]
    
    for val in test_values:
        converted = int(round(max(0, min(val, 65535))))
        print(f"  {val:8.2f} -> {converted:5d}")
    
    print()
    print("=== 测试完成 ===")

def test_response_parsing():
    """测试涂鸦云响应消息的解析"""
    
    print("\n=== 测试涂鸦云响应消息解析 ===\n")
    
    # 成功响应示例
    success_response = {
        "code": 0,
        "msg": "success",
        "msgId": "12345",
        "time": int(time.time() * 1000)
    }
    
    print("1. 成功响应:")
    print(json.dumps(success_response, indent=2))
    print(f"解析结果: Code={success_response['code']}, Message='{success_response['msg']}'")
    print()
    
    # 错误响应示例
    error_response = {
        "code": 1001,
        "msg": "参数类型不匹配",
        "msgId": "12345",
        "time": int(time.time() * 1000)
    }
    
    print("2. 错误响应:")
    print(json.dumps(error_response, indent=2))
    print(f"解析结果: Code={error_response['code']}, Message='{error_response['msg']}'")
    print()
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_tuya_payload_format()
    test_response_parsing()
