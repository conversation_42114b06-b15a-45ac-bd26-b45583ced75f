#!/usr/bin/env python3
"""
测试DP数据规范化功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.langgraph_def.graph_builder import normalize_numeric_dp

def test_normalize_numeric_dp():
    """测试数据规范化函数"""
    
    print("=== 测试DP数据规范化功能 ===\n")
    
    # 测试用例1: value类型带小数step，应该转换为float
    test_dp_1 = {
        "id": 101,
        "name": "温度",
        "code": "temperature",
        "type": "value",
        "step": "0.1",
        "multiple": "0",
        "range_min": "0",
        "range_max": "100"
    }
    
    print("测试用例1 - value类型带小数step:")
    print(f"输入: {test_dp_1}")
    result_1 = normalize_numeric_dp(test_dp_1.copy())
    print(f"输出: {result_1}")
    print(f"类型是否已转换为float: {result_1['type'] == 'float'}\n")
    
    # 测试用例2: value类型整数step，应该保持value
    test_dp_2 = {
        "id": 102,
        "name": "亮度",
        "code": "brightness",
        "type": "value",
        "step": "1",
        "multiple": "0",
        "range_min": "0",
        "range_max": "1000"
    }
    
    print("测试用例2 - value类型整数step:")
    print(f"输入: {test_dp_2}")
    result_2 = normalize_numeric_dp(test_dp_2.copy())
    print(f"输出: {result_2}")
    print(f"类型是否保持value: {result_2['type'] == 'value'}\n")
    
    # 测试用例3: value类型带小数multiple，应该转换为float
    test_dp_3 = {
        "id": 103,
        "name": "功率",
        "code": "power",
        "type": "value",
        "step": "1",
        "multiple": "0.1",
        "range_min": "0",
        "range_max": "2000"
    }
    
    print("测试用例3 - value类型带小数multiple:")
    print(f"输入: {test_dp_3}")
    result_3 = normalize_numeric_dp(test_dp_3.copy())
    print(f"输出: {result_3}")
    print(f"类型是否已转换为float: {result_3['type'] == 'float'}\n")
    
    # 测试用例4: 非value类型，应该不受影响
    test_dp_4 = {
        "id": 104,
        "name": "开关",
        "code": "switch",
        "type": "bool",
        "step": "0.1",  # 这个字段对bool类型无意义，但不应该影响处理
    }
    
    print("测试用例4 - 非value类型:")
    print(f"输入: {test_dp_4}")
    result_4 = normalize_numeric_dp(test_dp_4.copy())
    print(f"输出: {result_4}")
    print(f"类型是否保持bool: {result_4['type'] == 'bool'}\n")
    
    # 测试用例5: value类型空step，应该设置默认值
    test_dp_5 = {
        "id": 105,
        "name": "湿度",
        "code": "humidity",
        "type": "value",
        "step": "",
        "multiple": "",
        "range_min": "0",
        "range_max": "100"
    }
    
    print("测试用例5 - value类型空step:")
    print(f"输入: {test_dp_5}")
    result_5 = normalize_numeric_dp(test_dp_5.copy())
    print(f"输出: {result_5}")
    print(f"step是否设置为默认值1: {result_5['step'] == '1'}")
    print(f"multiple是否设置为默认值0: {result_5['multiple'] == '0'}\n")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_normalize_numeric_dp()
